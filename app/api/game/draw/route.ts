import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { verifyRecaptcha } from "@/libs/recaptcha";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { gameRecordId, recaptchaToken } = body;

    // Verify reCAPTCHA token first
    if (!recaptchaToken) {
      return NextResponse.json({ message: "請完成人機驗證" }, { status: 400 });
    }

    const isRecaptchaValid = await verifyRecaptcha(recaptchaToken);
    if (!isRecaptchaValid) {
      return NextResponse.json(
        { message: "人機驗證失敗，請重試" },
        { status: 400 },
      );
    }

    if (!gameRecordId) {
      return NextResponse.json({ message: "缺少遊戲記錄ID" }, { status: 400 });
    }

    // Check if the game record exists and belongs to the user
    const gameRecord = await prisma.gameRecord.findFirst({
      where: {
        id: gameRecordId,
        userId: userId,
      },
    });

    if (!gameRecord) {
      return NextResponse.json({ message: "遊戲記錄不存在" }, { status: 404 });
    }

    // Check if already drawn
    if (gameRecord.drawResult !== null) {
      return NextResponse.json({ message: "已經抽過獎了" }, { status: 400 });
    }

    // Get website config for lucky draw chance
    const config = await prisma.websiteConfig.findFirst({
      orderBy: {
        createdAt: "desc",
      },
    });

    const luckyDrawChance = config?.luckyDrawChance || 0.1; // Default 10%

    // Perform the draw
    const isWin = Math.random() < luckyDrawChance;
    let couponId = null;

    if (isWin) {
      // Find an available coupon (not assigned to any user)
      const availableCoupon = await prisma.coupon.findFirst({
        where: {
          userId: null,
        },
      });

      if (availableCoupon) {
        // Assign the coupon to the user
        await prisma.coupon.update({
          where: {
            id: availableCoupon.id,
          },
          data: {
            userId: userId,
          },
        });
        couponId = availableCoupon.id;
      }
    }

    // Update the game record with draw result
    const updatedGameRecord = await prisma.gameRecord.update({
      where: {
        id: gameRecordId,
      },
      data: {
        drawResult: couponId ? "WIN" : "LOSE",
        couponId: couponId,
      },
      include: {
        coupon: {
          select: {
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      result: couponId ? "win" : "lose",
      coupon: updatedGameRecord.coupon,
    });
  } catch (error) {
    console.error("Error performing draw:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
