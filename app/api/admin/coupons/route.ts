import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { Prisma } from "@prisma/client";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  const page = parseInt(req.nextUrl.searchParams.get("page") || "1");
  const pageSize = parseInt(req.nextUrl.searchParams.get("pageSize") || "20");
  const search = req.nextUrl.searchParams.get("search") || "";
  const status = req.nextUrl.searchParams.get("status") || "all"; // all, available, assigned

  try {
    // Build where clause for search and status filter
    const whereClause: Prisma.CouponFindManyArgs["where"] = {};

    if (search) {
      whereClause.OR = [
        {
          code: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          User: {
            nickname: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          User: {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
      ];
    }

    // Add status filter
    if (status === "available") {
      whereClause.userId = null;
    } else if (status === "assigned") {
      whereClause.userId = { not: null };
    }

    // Get total count for pagination
    const totalCount = await prisma.coupon.count({
      where: whereClause,
    });

    // Calculate pagination
    const skip = (page - 1) * pageSize;
    const totalPages = Math.ceil(totalCount / pageSize);

    const coupons = await prisma.coupon.findMany({
      where: whereClause,
      include: {
        User: {
          select: {
            id: true,
            nickname: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: pageSize,
    });

    const formattedCoupons = coupons.map((coupon) => ({
      id: coupon.id,
      code: coupon.code,
      createdAt: coupon.createdAt.toISOString(),
      updatedAt: coupon.updatedAt.toISOString(),
      status: coupon.userId ? "assigned" : "available",
      user: coupon.User,
    }));

    return NextResponse.json({
      data: formattedCoupons,
      pagination: {
        current: page,
        pageSize,
        total: totalCount,
        totalPages,
      },
      stats: {
        total: totalCount,
        available: await prisma.coupon.count({
          where: { userId: null },
        }),
        assigned: await prisma.coupon.count({
          where: { userId: { not: null } },
        }),
      },
    });
  } catch (error) {
    console.error("Error fetching coupons:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const body = await req.json();
    const { coupons } = body;

    if (!coupons || !Array.isArray(coupons) || coupons.length === 0) {
      return NextResponse.json(
        { message: "請提供有效的獎券代碼列表" },
        { status: 400 },
      );
    }

    // Validate coupon codes
    const validCoupons = coupons
      .filter((code) => typeof code === "string" && code.trim().length > 0)
      .map((code) => code.trim());

    if (validCoupons.length === 0) {
      return NextResponse.json(
        { message: "沒有有效的獎券代碼" },
        { status: 400 },
      );
    }

    // Check for duplicate codes in the database
    const existingCoupons = await prisma.coupon.findMany({
      where: {
        code: {
          in: validCoupons,
        },
      },
      select: {
        code: true,
      },
    });

    const existingCodes = existingCoupons.map((c) => c.code);
    const newCoupons = validCoupons.filter(
      (code) => !existingCodes.includes(code),
    );

    if (newCoupons.length === 0) {
      return NextResponse.json(
        {
          message: "所有獎券代碼都已存在",
          duplicates: existingCodes,
        },
        { status: 400 },
      );
    }

    // Create new coupons
    const result = await prisma.coupon.createMany({
      data: newCoupons.map((code) => ({ code })),
    });

    return NextResponse.json({
      message: `成功新增 ${result.count} 張獎券`,
      created: result.count,
      duplicates: existingCodes,
      total: validCoupons.length,
    });
  } catch (error) {
    console.error("Error creating coupons:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
