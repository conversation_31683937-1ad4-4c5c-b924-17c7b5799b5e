import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return new Response("請先登入", { status: 401 });
  }

  const body = await req.json();
  const { gameId, score, nickname } = body;

  // Create the game record
  const gameRecord = await prisma.gameRecord.create({
    data: {
      gameId,
      userId,
      score,
      nickname,
    },
  });

  return NextResponse.json({
    id: gameRecord.id,
    message: "記錄創建成功",
  });
}
