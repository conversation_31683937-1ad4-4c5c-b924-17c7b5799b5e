import Image from "next/image";
import { useState } from "react";
import { GameResultButton } from "@/app/components/buttons/game-result-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { Mask } from "@/app/components/mask";
import { Recaptcha } from "@/app/components/recaptcha";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { DrawResultDialog } from "./draw-result-dialog";
import { DrawResult } from "@prisma/client";

export const GameResult = ({
  gameTitle,
  nickname,
  gameId,
  score,
  gameRecordId,
  onPlayAgain,
}: {
  gameTitle: React.ReactNode;
  nickname: string;
  gameId: GameId;
  score: number;
  gameRecordId?: string;
  onPlayAgain: () => void;
}) => {
  const [drawResult, setDrawResult] = useState<DrawResult | undefined>(
    undefined,
  );
  const [couponCode, setCouponCode] = useState<string>("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [showRecaptcha, setShowRecaptcha] = useState(false);

  const handleDrawClick = () => {
    if (!gameRecordId) {
      alert("無法進行抽獎，請重新遊戲");
      return;
    }

    // Show reCAPTCHA when user clicks draw button
    setShowRecaptcha(true);
  };

  const performDraw = async (recaptchaToken: string) => {
    if (!gameRecordId) {
      alert("無法進行抽獎，請重新遊戲");
      return;
    }

    if (!recaptchaToken) {
      alert("請完成人機驗證");
      return;
    }

    setIsDrawing(true);
    try {
      const response = await fetch("/api/game/draw", {
        method: "POST",
        body: JSON.stringify({
          gameRecordId,
          recaptchaToken,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setDrawResult(
          result.result === "win" ? DrawResult.WIN : DrawResult.LOSE,
        );
        if (result.coupon) {
          setCouponCode(result.coupon.code);
        }
      } else {
        const error = await response.json();
        alert(error.message || "抽獎失敗");
      }
    } catch (error) {
      console.error("Draw error:", error);
      alert("網絡錯誤，請稍後再試");
    } finally {
      setIsDrawing(false);
      setShowRecaptcha(false);
    }
  };

  const handleRecaptchaVerify = (token: string | null) => {
    if (token) {
      // Automatically perform draw when reCAPTCHA is verified
      performDraw(token);
    }
  };

  const handleRecaptchaExpired = () => {
    alert("人機驗證已過期，請重新驗證");
  };

  const handleRecaptchaError = () => {
    alert("人機驗證發生錯誤，請重試");
  };

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <Mask visible />
        <div className="z-0 w-[73vw] h-[23.5vw] flex justify-center items-center">
          {gameTitle}
        </div>
        <div className="relative z-0 mt-[5vw]">
          <Image
            className="w-[67.5vw]"
            unoptimized
            alt=""
            src={imageUrl("/game-result-background.png")}
            width={728}
            height={1001}
          />
          <div className="absolute w-full h-full left-0 top-0 flex flex-col py-[16.5vw] px-[10vw] font-[700]">
            <div>
              <span className="text-[8vw] text-[#fff100] font-[1000] mr-[3vw]">
                {nickname}
              </span>
              <span className="text-[5vw]">您在</span>
            </div>
            <div className="mt-[3vw]">
              <span className="text-[5vw]">
                {
                  {
                    balance: "極限平衡 中堅持了",
                    catch: "威風接招 中獲得",
                    quiz: "威金森考驗 中拿下",
                  }[gameId]
                }
              </span>
            </div>
            <div className="text-[#fff100] font-[1000] mt-[2vw]">
              <span className="text-[14vw]">{score}</span>
              <span className="text-[12vw] relative bottom-[0.5vw]">
                {
                  {
                    balance: "秒",
                    catch: "分",
                    quiz: "分",
                  }[gameId]
                }
              </span>
            </div>
            <div>
              <span className="text-[5vw]">
                {
                  {
                    balance: "實在太威啦!",
                    catch: "真的有夠威!",
                    quiz: "簡直強氣炸裂!",
                  }[gameId]
                }
              </span>
            </div>
          </div>
          <div className="flex gap-[2vw] justify-center mt-[2vw]">
            <GameResultButton
              onClick={handleDrawClick}
              disabled={isDrawing || !gameRecordId || showRecaptcha}
            >
              {isDrawing
                ? "抽獎中..."
                : showRecaptcha
                  ? "請完成驗證"
                  : "分享立即抽"}
            </GameResultButton>
            <GameResultButton onClick={onPlayAgain}>再次挑戰</GameResultButton>
          </div>

          {/* reCAPTCHA Component */}
          {showRecaptcha && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <h3 className="text-lg font-bold mb-4 text-center text-black">
                  請完成人機驗證
                </h3>
                <Recaptcha
                  onVerify={handleRecaptchaVerify}
                  onExpired={handleRecaptchaExpired}
                  onError={handleRecaptchaError}
                  size="normal"
                  theme="light"
                />
                <button
                  onClick={() => setShowRecaptcha(false)}
                  className="mt-4 w-full px-4 py-2 bg-gray-300 text-black rounded hover:bg-gray-400"
                >
                  取消
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {drawResult && (
        <DrawResultDialog
          onClose={() => setDrawResult(undefined)}
          onPlayAgain={onPlayAgain}
          drawResult={drawResult}
          code={couponCode || ""}
        />
      )}
    </>
  );
};
